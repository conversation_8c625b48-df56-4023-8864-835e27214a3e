# WebSocket 一对一聊天应用
from flask import Flask, render_template, request
from flask_socketio import So<PERSON><PERSON>, emit, join_room, leave_room, disconnect
from datetime import datetime
import uuid

# 创建Flask应用
app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-for-chat'

# 初始化SocketIO
socketio = SocketIO(app, cors_allowed_origins="*")

# 存储在线用户和聊天室信息
online_users = {}  # {session_id: {'username': str, 'user_id': str}}
private_rooms = {}  # {room_id: {'users': [user1, user2], 'messages': []}}

@app.route('/')
def index():
    """聊天首页"""
    return render_template('chat/index.html')

@app.route('/chat')
def chat_room():
    """聊天室页面"""
    username = request.args.get('username')
    if not username:
        return redirect(url_for('index'))
    return render_template('chat/room.html', username=username)

# WebSocket 事件处理

@socketio.on('connect')
def on_connect():
    """用户连接"""
    print(f'用户连接: {request.sid}')

@socketio.on('disconnect')
def on_disconnect():
    """用户断开连接"""
    if request.sid in online_users:
        user_info = online_users[request.sid]
        username = user_info['username']
        
        # 从在线用户列表移除
        del online_users[request.sid]
        
        # 通知其他用户该用户已下线
        emit('user_left', {
            'username': username,
            'message': f'{username} 已离开聊天室',
            'online_users': list(online_users.values())
        }, broadcast=True)
        
        print(f'用户断开连接: {username} ({request.sid})')

@socketio.on('join_chat')
def on_join_chat(data):
    """用户加入聊天"""
    username = data['username']
    user_id = str(uuid.uuid4())
    
    # 检查用户名是否已存在
    existing_usernames = [user['username'] for user in online_users.values()]
    if username in existing_usernames:
        emit('error', {'message': '用户名已存在，请选择其他用户名'})
        return
    
    # 添加到在线用户列表
    online_users[request.sid] = {
        'username': username,
        'user_id': user_id,
        'session_id': request.sid
    }
    
    # 通知用户加入成功
    emit('join_success', {
        'username': username,
        'user_id': user_id,
        'online_users': list(online_users.values())
    })
    
    # 通知其他用户有新用户加入
    emit('user_joined', {
        'username': username,
        'message': f'{username} 加入了聊天室',
        'online_users': list(online_users.values())
    }, broadcast=True, include_self=False)
    
    print(f'用户加入: {username} ({request.sid})')

@socketio.on('request_private_chat')
def on_request_private_chat(data):
    """请求私聊"""
    target_username = data['target_username']
    requester = online_users.get(request.sid)
    
    if not requester:
        emit('error', {'message': '用户未登录'})
        return
    
    # 查找目标用户
    target_user = None
    target_session_id = None
    for session_id, user_info in online_users.items():
        if user_info['username'] == target_username:
            target_user = user_info
            target_session_id = session_id
            break
    
    if not target_user:
        emit('error', {'message': '目标用户不在线'})
        return
    
    # 创建私聊房间ID
    room_id = f"private_{min(requester['user_id'], target_user['user_id'])}_{max(requester['user_id'], target_user['user_id'])}"
    
    # 检查是否已存在该私聊房间
    if room_id not in private_rooms:
        private_rooms[room_id] = {
            'users': [requester, target_user],
            'messages': []
        }
    
    # 将两个用户加入私聊房间
    join_room(room_id)
    socketio.server.enter_room(target_session_id, room_id)
    
    # 通知双方私聊开始
    emit('private_chat_started', {
        'room_id': room_id,
        'partner': target_user,
        'messages': private_rooms[room_id]['messages']
    })
    
    emit('private_chat_started', {
        'room_id': room_id,
        'partner': requester,
        'messages': private_rooms[room_id]['messages']
    }, room=target_session_id)
    
    print(f'私聊开始: {requester["username"]} <-> {target_user["username"]} (房间: {room_id})')

@socketio.on('send_private_message')
def on_send_private_message(data):
    """发送私聊消息"""
    room_id = data['room_id']
    message_text = data['message']
    sender = online_users.get(request.sid)
    
    if not sender:
        emit('error', {'message': '用户未登录'})
        return
    
    if room_id not in private_rooms:
        emit('error', {'message': '聊天室不存在'})
        return
    
    # 创建消息对象
    message = {
        'id': str(uuid.uuid4()),
        'sender': sender['username'],
        'message': message_text,
        'timestamp': datetime.now().isoformat(),
        'formatted_time': datetime.now().strftime('%H:%M:%S')
    }
    
    # 保存消息到房间
    private_rooms[room_id]['messages'].append(message)
    
    # 发送消息到房间内的所有用户
    emit('receive_private_message', {
        'room_id': room_id,
        'message': message
    }, room=room_id)
    
    print(f'私聊消息: {sender["username"]} -> 房间 {room_id}: {message_text}')

@socketio.on('leave_private_chat')
def on_leave_private_chat(data):
    """离开私聊"""
    room_id = data['room_id']
    user = online_users.get(request.sid)
    
    if user and room_id in private_rooms:
        leave_room(room_id)
        
        # 通知房间内其他用户
        emit('user_left_private_chat', {
            'username': user['username'],
            'message': f'{user["username"]} 离开了私聊'
        }, room=room_id)
        
        print(f'用户离开私聊: {user["username"]} 离开房间 {room_id}')

if __name__ == '__main__':
    print("WebSocket聊天应用启动中...")
    print("访问 http://127.0.0.1:5001 开始聊天")
    print("按 Ctrl+C 停止应用")
    
    # 启动SocketIO服务器
    socketio.run(app, debug=True, host='127.0.0.1', port=5001)
