<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket 聊天室</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .container {
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            text-align: center;
            max-width: 400px;
            width: 90%;
        }

        .logo {
            font-size: 3rem;
            margin-bottom: 1rem;
        }

        h1 {
            color: #333;
            margin-bottom: 0.5rem;
            font-size: 2rem;
        }

        .subtitle {
            color: #666;
            margin-bottom: 2rem;
            font-size: 1rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
            text-align: left;
        }

        label {
            display: block;
            margin-bottom: 0.5rem;
            color: #333;
            font-weight: 500;
        }

        input[type="text"] {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        input[type="text"]:focus {
            outline: none;
            border-color: #667eea;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            cursor: pointer;
            transition: transform 0.2s ease;
            width: 100%;
        }

        .btn:hover {
            transform: translateY(-2px);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .features {
            margin-top: 2rem;
            text-align: left;
        }

        .features h3 {
            color: #333;
            margin-bottom: 1rem;
            font-size: 1.2rem;
        }

        .features ul {
            list-style: none;
            color: #666;
        }

        .features li {
            margin-bottom: 0.5rem;
            padding-left: 1.5rem;
            position: relative;
        }

        .features li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #10a37f;
            font-weight: bold;
        }

        .error {
            color: #e74c3c;
            margin-top: 0.5rem;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">💬</div>
        <h1>WebSocket 聊天室</h1>
        <p class="subtitle">实时一对一聊天体验</p>
        
        <form id="joinForm">
            <div class="form-group">
                <label for="username">请输入您的昵称：</label>
                <input type="text" id="username" name="username" placeholder="输入昵称..." maxlength="20" required>
                <div id="error-message" class="error" style="display: none;"></div>
            </div>
            
            <button type="submit" class="btn" id="joinBtn">
                进入聊天室
            </button>
        </form>

        <div class="features">
            <h3>功能特色</h3>
            <ul>
                <li>实时消息传递</li>
                <li>一对一私聊</li>
                <li>在线用户列表</li>
                <li>消息历史记录</li>
                <li>用户上线/下线通知</li>
            </ul>
        </div>
    </div>

    <script>
        const joinForm = document.getElementById('joinForm');
        const usernameInput = document.getElementById('username');
        const joinBtn = document.getElementById('joinBtn');
        const errorMessage = document.getElementById('error-message');

        // 表单提交处理
        joinForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const username = usernameInput.value.trim();
            
            // 验证用户名
            if (!username) {
                showError('请输入昵称');
                return;
            }
            
            if (username.length < 2) {
                showError('昵称至少需要2个字符');
                return;
            }
            
            if (username.length > 20) {
                showError('昵称不能超过20个字符');
                return;
            }
            
            // 检查特殊字符
            if (!/^[a-zA-Z0-9\u4e00-\u9fa5_-]+$/.test(username)) {
                showError('昵称只能包含字母、数字、中文、下划线和连字符');
                return;
            }
            
            // 禁用按钮，防止重复提交
            joinBtn.disabled = true;
            joinBtn.textContent = '正在进入...';
            
            // 跳转到聊天室
            window.location.href = `/chat?username=${encodeURIComponent(username)}`;
        });

        // 显示错误信息
        function showError(message) {
            errorMessage.textContent = message;
            errorMessage.style.display = 'block';
            usernameInput.focus();
        }

        // 输入时清除错误信息
        usernameInput.addEventListener('input', function() {
            errorMessage.style.display = 'none';
            joinBtn.disabled = false;
            joinBtn.textContent = '进入聊天室';
        });

        // 页面加载时聚焦到输入框
        window.addEventListener('load', function() {
            usernameInput.focus();
        });

        // 回车键提交
        usernameInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                joinForm.dispatchEvent(new Event('submit'));
            }
        });
    </script>
</body>
</html>
